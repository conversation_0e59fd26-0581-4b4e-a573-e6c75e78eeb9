/**
 * Mobile Warning Component
 * Displays a dismissable warning message for users on mobile devices
 */

(function(global) {
  'use strict';

  let warningElement = null;
  let isInitialized = false;

  /**
   * Create the necessary CSS styles for the mobile warning
   */
  function injectCSS() {
    if (document.getElementById('mobile-warning-styles')) {
      return;
    }

    const styleEl = document.createElement('style');
    styleEl.id = 'mobile-warning-styles';
    styleEl.textContent = `
      .mobile-warning {
        position: fixed;
        top: 70px; /* Position below the header */
        left: 0;
        right: 0;
        background-color: #FEF3C7; /* Amber/yellow background */
        color: #92400E; /* Amber/brown text */
        border-bottom: 1px solid #FCD34D;
        padding: 12px 16px;
        z-index: 100;
        font-size: 0.875rem;
        transform: translateY(-100%);
        transition: transform 0.3s ease-in-out;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .mobile-warning.show {
        transform: translateY(0);
      }

      .mobile-warning-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 100%;
        margin: 0 auto;
      }

      .mobile-warning-message {
        flex: 1;
        padding-right: 12px;
      }

      .mobile-warning-icon {
        flex-shrink: 0;
        margin-right: 12px;
        color: #92400E;
      }

      .mobile-warning-close {
        background: none;
        border: none;
        color: #92400E;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .mobile-warning-close:hover {
        background-color: rgba(146, 64, 14, 0.1);
      }

      .mobile-warning-checkbox-container {
        display: flex;
        align-items: center;
        margin-top: 8px;
        font-size: 0.75rem;
      }

      .mobile-warning-checkbox {
        margin-right: 6px;
      }
    `;

    document.head.appendChild(styleEl);
  }

  /**
   * Create the HTML structure for the mobile warning
   * @returns {HTMLElement} The warning element
   */
  function createWarningElement() {
    const warningEl = document.createElement('div');
    warningEl.className = 'mobile-warning';
    warningEl.innerHTML = `
      <div class="mobile-warning-content">
        <div class="mobile-warning-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <div class="mobile-warning-message">
          <p>Our platform is optimized for desktop environments. While mobile access is available, we recommend switching to a computer for complete functionality and optimal performance.</p>
          <div class="mobile-warning-checkbox-container">
            <input type="checkbox" id="mobile-warning-dont-show" class="mobile-warning-checkbox">
            <label for="mobile-warning-dont-show">Don't show this message again</label>
          </div>
        </div>
        <button class="mobile-warning-close" aria-label="Dismiss warning">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    `;

    return warningEl;
  }

  /**
   * Initialize event listeners for the warning element
   */
  function initializeEventListeners() {
    const closeButton = warningElement.querySelector('.mobile-warning-close');
    const dontShowCheckbox = warningElement.querySelector('#mobile-warning-dont-show');

    closeButton.addEventListener('click', () => {
      hideWarning();
      
      // If checkbox is checked, permanently dismiss the warning
      if (dontShowCheckbox.checked && window.MobileDetection) {
        window.MobileDetection.dismissWarning(true);
      } else if (window.MobileDetection) {
        // Otherwise, dismiss just for this session
        window.MobileDetection.dismissWarning(false);
      }
    });
  }

  /**
   * Show the mobile warning
   */
  function showWarning() {
    // Don't show if already dismissed
    if (window.MobileDetection && 
        (window.MobileDetection.hasUserDismissedWarning() || 
         window.MobileDetection.isWarningDismissedForSession())) {
      return;
    }

    if (!isInitialized) {
      initialize();
    }

    // Add to DOM if not already there
    if (!document.body.contains(warningElement)) {
      document.body.appendChild(warningElement);
    }

    // Trigger animation
    setTimeout(() => {
      warningElement.classList.add('show');
    }, 10);
  }

  /**
   * Hide the mobile warning
   */
  function hideWarning() {
    if (!warningElement) return;
    
    warningElement.classList.remove('show');
    
    // Remove from DOM after animation completes
    setTimeout(() => {
      if (warningElement.parentNode) {
        warningElement.parentNode.removeChild(warningElement);
      }
    }, 300);
  }

  /**
   * Initialize the mobile warning component
   */
  function initialize() {
    if (isInitialized) return;
    
    injectCSS();
    warningElement = createWarningElement();
    initializeEventListeners();
    
    isInitialized = true;
  }

  // Public API
  global.MobileWarning = {
    show: showWarning,
    hide: hideWarning,
    initialize: initialize
  };

})(typeof window !== 'undefined' ? window : global);
