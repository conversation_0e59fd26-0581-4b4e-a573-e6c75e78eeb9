/* Fade-in animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Fade-out animation */
@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Slide-up animation */
@keyframes slideUp {
  from { 
    transform: translateY(10px);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

/* Staggered row animations */
.table-row {
  opacity: 0;
  animation: slideUp 0.4s ease-out forwards;
}

/* Apply staggered delay to rows */
.table-row:nth-child(1) { animation-delay: 0.1s; }
.table-row:nth-child(2) { animation-delay: 0.15s; }
.table-row:nth-child(3) { animation-delay: 0.2s; }
.table-row:nth-child(4) { animation-delay: 0.25s; }
.table-row:nth-child(5) { animation-delay: 0.3s; }
.table-row:nth-child(6) { animation-delay: 0.35s; }
.table-row:nth-child(7) { animation-delay: 0.4s; }
.table-row:nth-child(8) { animation-delay: 0.45s; }
.table-row:nth-child(9) { animation-delay: 0.5s; }
.table-row:nth-child(10) { animation-delay: 0.55s; }

/* Smooth transition for all elements */
.card, .card-content, .card-empty, .skeleton-card, .skeleton-row, 
.table-header, #loading-overlay {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Smooth card animations */
.dashboard-card {
  opacity: 0;
  transform: translateY(10px);
  animation: slideUp 0.5s ease-out forwards;
}

.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }

/* Loading overlay styling */
#loading-overlay {
  transition: opacity 0.4s ease;
  opacity: 1;
}

#loading-overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

/* Tooltip transitions */
.tooltip {
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Table row hover effect */
.table-row-hover {
  transition: background-color 0.2s ease-in-out;
}

/* Header fading in animation */
.table-header {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.1s forwards;
}

/* Hide skeleton elements with transition */
.skeleton-fade-out {
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Animation utility classes */
.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-up {
  opacity: 0;
  transform: translateY(10px);
  animation: slideUp 0.5s ease-out forwards;
}

/* Counter animation */
@keyframes pulse-once {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.text-4xl {
  animation: pulse-once 0.5s ease-out;
}

/* Modal animation */
@keyframes modalFadeIn {
  from { 
    opacity: 0;
    transform: translateY(-20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

#email-modal > div {
  animation: modalFadeIn 0.3s ease-out;
}

/* Button hover effects */
button:not(:disabled) {
  transition: all 0.2s ease;
}

.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* All assessments button animation */
.all-assessments-btn {
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: translateX(10px);
}

.all-assessments-btn.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Improve skeleton transition */
.skeleton-card, .skeleton-row, .card-content, .card-empty {
  transition: opacity 0.3s ease-out, display 0.3s ease-out;
}

.skeleton-card.hidden {
  opacity: 0;
}

/* Smoother transitions for visibility changes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Display state animation helpers */
.fade-out {
  animation: fadeOut 0.3s ease-out forwards;
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Skills Gap Modal animations */
.skills-modal-overlay {
  opacity: 0;
  transition: opacity 0.4s ease;
}

.skills-modal-overlay.visible {
  opacity: 1;
}

.skills-modal-content {
  opacity: 0;
  transform: scale(0.95);
  transition: all 0.4s ease;
}

.skills-modal-overlay.visible .skills-modal-content {
  opacity: 1;
  transform: scale(1);
}

/* Add a fade-in animation to modal elements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.skills-competency-card {
  animation: fadeInUp 0.4s ease-out forwards;
  opacity: 0;
}

/* Add staggered animation for cards */
.skills-competency-card:nth-child(1) { animation-delay: 0.1s; }
.skills-competency-card:nth-child(2) { animation-delay: 0.15s; }
.skills-competency-card:nth-child(3) { animation-delay: 0.2s; }
.skills-competency-card:nth-child(4) { animation-delay: 0.25s; }
.skills-competency-card:nth-child(5) { animation-delay: 0.3s; }
