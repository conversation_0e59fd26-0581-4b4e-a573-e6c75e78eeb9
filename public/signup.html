<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - SkillsAssess</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="signup.css">
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    <script src="loading-overlay.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Lottie for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.14/lottie.min.js"></script>
</head>
<body>
    <div class="main-container">
        <div class="signup-container">
            <div class="signup-heading">
                <h3>Create your account</h3>
                <p>Start Your Team's Journey</p>
            </div>

            <form id="signupForm">
                <div class="input-group">
                    <input type="text" id="firstname" placeholder="First Name" required>
                    <div id="firstnameError" class="error-message"></div>
                </div>

                <div class="input-group">
                    <input type="text" id="lastname" placeholder="Last Name" required>
                    <div id="lastnameError" class="error-message"></div>
                </div>

                <div class="input-group">
                    <input type="text" id="company" placeholder="Company Name" required>
                    <div id="companyError" class="error-message"></div>
                </div>


            <div class="relative">
                <input type="email"
                id="email"
                 name="email"
                class="w-full p-4 border border-[#e5e7eb] bg-[#f3f4f6] text-[#1f2937] rounded-xl outline-none transition-all duration-300 focus:border-[#6366f1] focus:bg-white focus:shadow-[0_0_0_3px_rgba(99,102,241,0.1)]"
                placeholder="Work email"
                required>

    <!-- Success Icon -->
                 <svg id="emailValidIcon"
                    class="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#10B981] hidden transition-opacity duration-200"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor">
                    <path stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"/>
                </svg>

    <!-- Error Icon -->
            <svg id="emailInvalidIcon"
                class="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#ef4444] hidden transition-opacity duration-200"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor">
                <path stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </div>

                <div class="input-group password-group">
                    <input type="password" id="password" placeholder="Password" required>
                    <button type="button" class="password-toggle">
                        <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        <svg class="eye-closed hidden" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                            <line x1="1" y1="1" x2="23" y2="23"></line>
                        </svg>
                    </button>
                    <div id="passwordError" class="error-message"></div>
                </div>

                <div class="input-group password-group">
                    <input type="password" id="confirmPassword" placeholder="Confirm Password" required>
                    <button type="button" class="password-toggle">
                        <svg class="eye-open" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        <svg class="eye-closed hidden" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                            <line x1="1" y1="1" x2="23" y2="23"></line>
                        </svg>
                    </button>
                    <div id="confirmPasswordError" class="error-message"></div>
                </div>

                <div class="terms-privacy">
                    <label class="checkbox-container">
                        <input type="checkbox" id="termsCheckbox" required>
                        <span class="checkmark"></span>
                        <a href="https://skillsassess.ai/terms-and-conditions/" class="terms-link" target="_blank" rel="noopener noreferrer">Terms of Service</a> and
                    <a href="https://skillsassess.ai/privacy-policy/" class="privacy-link" target="_blank" rel="noopener noreferrer">Privacy Policy</a>
                    </label>
                </div>

                <div class="signup-button">
                    <button type="submit">Create Account</button>
                </div>

                <div class="login-link">
                    Already have an account? <a href="index.html">Sign in</a>
                </div>
            </form>
        </div>
    </div>

    <div id="loading-overlay" style="display: none;">
        <div id="loading-animation" class="loading-animation"></div>
        <div class="loading-text">Creating account...</div>
    </div>

    <div id="success-overlay" class="success-overlay hidden">
        <svg class="success-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <!-- Checkmark Icon -->
            <path fill="none" stroke="currentColor" stroke-width="2" d="M5 13l4 4L19 7"/>
        </svg>
        <p>Account created successfully!</p>
    </div>



    <script src="signup.js"></script>
    <!-- Updated responsive footer with better scaling and positioning -->
    <footer class="fixed left-5 bottom-5" id="logo-footer">
        <img src="logosmall.png" alt="Logo" class="logo-img w-auto max-w-[120px]" id="footer-logo-img">
    </footer>

    <script>
        // Add event listener to check if logo overlaps with form content
        window.addEventListener('resize', checkLogoOverlap);
        window.addEventListener('load', checkLogoOverlap);

        function checkLogoOverlap() {
            const logo = document.getElementById('footer-logo-img');
            const form = document.getElementById('signupForm');
            const footer = document.getElementById('logo-footer');

            // Get viewport width
            const viewportWidth = window.innerWidth;

            // Automatically hide logo on very small screens
            if (viewportWidth <= 380) {
                footer.style.display = 'none';
                return;
            } else {
                footer.style.display = 'block';
            }

            // Adjust opacity based on screen size
            if (viewportWidth <= 480) {
                logo.style.opacity = '0.7';
                logo.style.maxWidth = '80px';
            } else if (viewportWidth <= 640) {
                logo.style.opacity = '0.8';
                logo.style.maxWidth = '100px';
            } else {
                logo.style.opacity = '0.9';
                logo.style.maxWidth = '120px';
            }
        }
    </script>
</body>
</html>