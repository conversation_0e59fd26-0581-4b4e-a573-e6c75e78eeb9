/* User menu sidebar styles */
#user-menu {
  z-index: 100;
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.08);
  width: 340px;
  overflow-y: auto;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease;
  opacity: 0;
  border-radius: 12px 0 0 12px;
}

#user-menu.show {
  transform: translateX(0);
  opacity: 1;
}

/* User profile section */
#user-menu .px-4.py-2 {
  background-color: #f9fafb;
  padding: 18px;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 12px 0 0 0;
}

/* Close button */
#user-menu-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

#user-menu-close:hover {
  background-color: #e5e7eb;
}

#user-menu-close svg {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

/* User avatar */
#user-menu .avatar-container {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  border: 2px solid white;
}

#user-menu .avatar-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* User name and company */
#user-menu p.font-semibold {
  color: #111827;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 3px;
  line-height: 1.2;
}

#user-menu p.text-xs {
  color: #6b7280;
  font-size: 13px;
  margin: 0;
}

/* Active status */
#user-menu .inline-flex.items-center {
  margin-top: 4px;
}

#user-menu .inline-block.w-2.h-2 {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

/* Section dividers */
#user-menu .border-t {
  border-top: 1px solid #f0f0f0;
  margin: 0;
}

/* Menu sections */
#user-menu .subscription-section,
#user-menu .demo-section,
#user-menu .credits-section {
  padding: 16px;
}

/* Section titles and content */
#user-menu .flex.items-center {
  margin-bottom: 6px;
}

#user-menu .font-medium {
  font-weight: 500;
  font-size: 13px;
  color: #4b5563;
}

#user-menu .font-semibold {
  font-weight: 500;
  color: #1f2937;
  font-size: 13px;
}

/* Cancelled subscription text */
#user-menu .subscription-section .font-semibold {
  font-size: 11px;
}

/* Menu items */
#user-menu a.menu-item,
#user-menu #edit-profile,
#user-menu #user-logout {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #374151;
  font-size: 14px;
  transition: background-color 0.15s ease, opacity 0.2s ease, transform 0.2s ease;
  text-decoration: none;
  border-radius: 8px;
  margin: 6px 0;
}

#user-menu a.menu-item:hover,
#user-menu #edit-profile:hover {
  background-color: #f3f4f6;
}

#user-menu #user-logout {
  color: #dc2626;
}

#user-menu #user-logout:hover {
  background-color: #fef2f2;
}

/* Icons in menu items */
#user-menu a.menu-item svg,
#user-menu a.menu-item img {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

/* Toggle switch styling */
#demo-toggle-switch {
  width: 36px;
  height: 18px;
  border-radius: 10px;
  position: relative;
  transition: background-color 0.2s;
}

#demo-toggle-switch .dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

#demo-toggle-switch.bg-blue-600 {
  background-color: #1547bb !important;
}

/* Cancellation notice */
.subscription-section .text-amber-600 {
  display: flex;
  align-items: center;
  font-size: 10px;
  padding: 4px 8px;
  background-color: #fffbeb;
  border-radius: 8px;
  margin-top: 6px;
  margin-bottom: 6px;
}

/* Buttons */
#user-menu button {
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Resubscribe button */
#resubscribeBtn {
  background-color: #10b981;
  color: white;
  border: none;
}

#resubscribeBtn:hover {
  background-color: #059669;
}

/* Vertical cancellation buttons */
#cancellation-subscription-buttons.flex-col button {
  padding: 6px 10px;
  margin-bottom: 0;
}

/* Manage subscription button */
#manageSubscriptionBtn {
  border: 1px solid #1547bb;
  color: #1547bb;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 6px;
}

#manageSubscriptionBtn:hover {
  background-color: #eff6ff;
}

#manageSubscriptionBtn svg {
  margin-right: 4px;
  width: 14px;
  height: 14px;
}

/* Sidebar backdrop */
#user-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s cubic-bezier(0.16, 1, 0.3, 1), visibility 0.3s ease;
  backdrop-filter: blur(1px);
}

#user-menu-backdrop.show {
  opacity: 1;
  visibility: visible;
}

/* Credits badge */
.credits-section .flex.items-center .font-semibold {
  background-color: #f3f4f6;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  color: #1547bb;
  font-size: 11px;
}

/* Responsive styles */
@media (max-width: 640px) {
  #user-menu {
    width: 300px;
  }
}

/* Dark mode support */
.dark #user-menu {
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark #user-menu .px-4.py-2 {
  background-color: #111827;
  border-bottom: 1px solid #1f2937;
}

.dark #user-menu-close {
  background-color: #374151;
}

.dark #user-menu-close:hover {
  background-color: #4b5563;
}

.dark #user-menu-close svg {
  color: #9ca3af;
}

.dark #user-menu p.font-semibold {
  color: #f1f5f9;
}

.dark #user-menu p.text-xs {
  color: #9ca3af;
}

.dark #user-menu .border-t {
  border-top: 1px solid #1f2937;
}

.dark #user-menu a.menu-item,
.dark #user-menu #edit-profile {
  color: #e2e8f0;
}

.dark #user-menu a.menu-item:hover,
.dark #user-menu #edit-profile:hover {
  background-color: #1f2937;
}

.dark #user-menu #user-logout {
  color: #f87171;
}

.dark #user-menu #user-logout:hover {
  background-color: rgba(248, 113, 113, 0.1);
}
